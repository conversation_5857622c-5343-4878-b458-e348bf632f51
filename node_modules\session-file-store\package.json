{"name": "session-file-store", "version": "1.5.0", "description": "Session file store is a provision for storing session data in the session file", "keywords": ["session", "file", "store", "express", "connect"], "main": "index.js", "private": false, "repository": {"type": "git", "url": "https://github.com/valery-barysok/session-file-store"}, "bugs": {"url": "https://github.com/valery-barysok/session-file-store/issues"}, "homepage": "https://github.com/valery-barysok/session-file-store", "files": ["lib/", "LICENSE", "README.md", "index.js"], "author": "<PERSON><PERSON> Barysok <<EMAIL>>", "contributors": [{"name": "<PERSON> (wespen)"}, {"name": "<PERSON> (bchr02)"}, {"name": "<PERSON> (r-murphy)"}, {"name": "<PERSON><PERSON><PERSON> (ndanson)"}, {"name": "<PERSON><PERSON><PERSON> (arnoo)"}, {"name": "Guson (gusonyang)"}, {"name": "<PERSON> (PhilippSpo)"}, {"name": "<PERSON> (tyoung86)"}, {"name": "<PERSON><PERSON><PERSON> (Laastine)"}], "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc mocha --reporter spec --check-leaks test/", "test-tap": "mocha --reporter tap --check-leaks test/"}, "license": "Apache-2.0", "engines": {"node": ">= 6"}, "dependencies": {"bagpipe": "^0.3.5", "fs-extra": "^8.0.1", "kruptein": "^2.0.4", "object-assign": "^4.1.1", "retry": "^0.12.0", "write-file-atomic": "3.0.3"}, "devDependencies": {"cbor-sync": "^1.0.2", "chai": "^4.2.0", "coveralls": "^3.0.4", "nyc": "^14.1.1", "lodash.clone": "^4.3.1", "mocha": "^6.1.4"}}