import { Injectable } from "@nestjs/common";
import { PrismaService } from "src/prisma/prisma.service";

type CreateUserRefreshTokenDto = {
    userId: string;
    token: string;
    expiresAt: Date;
    ipAddress: string | null;
    userAgent: string | null;
};

type RevokeUserRefreshTokenDto = {
    id: string;
    reason: string | null;
};

@Injectable()
export class UserRefreshTokenService {
    constructor(private readonly prisma: PrismaService) {}

    async create(createUserRefreshTokenDto: CreateUserRefreshTokenDto) {
        const userRefreshToken = await this.prisma.userRefreshToken.create({
            data: {
                userId: createUserRefreshTokenDto.userId,
                token: createUserRefreshTokenDto.token,
                expiresAt: createUserRefreshTokenDto.expiresAt,
                ipAddress: createUserRefreshTokenDto.ipAddress,
                userAgent: createUserRefreshTokenDto.userAgent,
            },
        });

        return userRefreshToken;
    }

    async findAll(dto: {
        params: {
            userId: string;
        };
    }) {
        const userRefreshTokens = await this.prisma.userRefreshToken.findMany({
            where: {
                userId: dto.params.userId,
                deletedAt: null,
            },
        });

        return userRefreshTokens;
    }

    async findByValue(value: string) {
        const where = {
            token: value,
            deletedAt: null,
        };

        console.log({ where });

        const userRefreshToken = await this.prisma.userRefreshToken.findFirst({
            where: where,
            include: {
                user: true,
            },
        });

        return userRefreshToken;
    }

    async softDelete(id: string) {
        await this.prisma.userRefreshToken.update({
            where: {
                id,
            },
            data: {
                deletedAt: new Date(),
            },
        });

        return true;
    }

    async delete(id: string) {
        await this.prisma.userRefreshToken.delete({
            where: { id },
        });

        return true;
    }

    async revoke(dto: RevokeUserRefreshTokenDto) {
        await this.prisma.userRefreshToken.update({
            where: { id: dto.id },
            data: {
                revokedAt: new Date(),
                revokeReason: dto.reason,
            },
        });

        return true;
    }
}
