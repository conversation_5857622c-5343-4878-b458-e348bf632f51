scalar DateTime @specifiedBy(url: "https://scalars.graphql.org/andimarek/date-time")

"image-source"
enum ImageSource {
    fs
    s3
}

"image"
type Image {
    id: ID!

    source: ImageSource!
    url: String!

    createdAt: DateTime!
    updatedAt: DateTime!
}

"tag"
type Tag {
    id: ID!

    createdAt: DateTime!
    updatedAt: DateTime!
}

"user-title"
type UserTitle {
    id: ID!

    color: String

    createdAt: DateTime!
    updatedAt: DateTime!
}

"post-status"
enum PostStatus {
    draft
    published
    archived
}

"post"
type Post {
    id: ID!

    images: [Image!]!

    status: PostStatus!

    publishedAt: DateTime

    createdAt: DateTime!
    updatedAt: DateTime!
}

"merchandise-status"
enum MerchandiseStatus {
    hidden
    shown
}

"merchandise"
type Merchandise {
    id: ID!

    category: String!

    price: Int!

    images: [Image!]!
    tags: [Tag!]!

    status: MerchandiseStatus!

    createdAt: DateTime!
    updatedAt: DateTime!
}

"""
Localization

Image

Tag

User
UserTitle

Commune
CommuneMember

Voting
VotingOption
Vote

Post
Merchandise
"""

# type Query {
#   getMerchandise(id: ID!): Merchandise
#   getMerchandises: [Merchandise!]!
# }

type Query {
    test: Boolean
}
