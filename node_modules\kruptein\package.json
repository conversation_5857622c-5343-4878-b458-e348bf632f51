{"name": "kruptein", "version": "2.2.3", "description": "crypto; from kruptein to hide or conceal", "keywords": ["crypto", "cryptography", "cryptr", "crypter", "encryption", "decryption", "encrypt", "decrypt", "AES", "GCM", "authenticated", "authenticate", "unicode", "symmetric", "cipher", "key derivation", "scrypt", "pbkdf2", "security", "asn.1"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jas-/kruptein.git"}, "bugs": {"url": "https://github.com/jas-/kruptein/issues"}, "devDependencies": {"expect.js": "^0.3.1", "mocha": "^7.1.1", "nyc": "^15.1.0"}, "scripts": {"test": "nyc mocha test/*.js"}, "engines": {"node": ">6"}, "dependencies": {"asn1.js": "^5.4.1"}}