import { ExecutionContext, Injectable, CanActivate, UnauthorizedException } from "@nestjs/common";
import { GqlExecutionContext } from "@nestjs/graphql";

@Injectable()
export class GqlSessionAuthGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean {
        const ctx = GqlExecutionContext.create(context);
        const request = ctx.getContext().req;

        if (!request.session || !request.session.user) {
            throw new UnauthorizedException();
        }

        // Set user on request for compatibility with existing decorators
        request.user = request.session.user;

        return true;
    }
}
