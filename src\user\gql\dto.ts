import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
import { UserRole } from "@prisma/client";

export const typename = "User";

export const UserTitle = z.object({
    __typename: z.literal("UserTitle").default("UserTitle"),
    id: ZodHelper.Uuid,
    color: z.string().nullable(),
    isActive: z.boolean(),
    createdAt: ZodHelper.ToDateTime,
    updatedAt: ZodHelper.ToDateTime,
});

export type User = ZodHelper.Infer<typeof User>;
export const User = z.object({
    __typename: z.literal(typename).default(typename),
    id: ZodHelper.Uuid,
    email: ZodHelper.Email,
    role: z.nativeEnum(UserRole),
    name: ZodHelper.Localizations,
    description: ZodHelper.Localizations,
    images: z
        .array(
            z.object({
                id: ZodHelper.Uuid,
                url: z.string(),
            }),
        )
        .optional(),
    titles: z.array(UserTitle),
    createdAt: <PERSON>od<PERSON>elper.ToDateTime,
    updatedAt: ZodHelper.ToDateTime,
});

export const Users = z.array(User);
