{"name": "bagpipe", "version": "0.3.5", "description": "Concurrency limit", "keywords": ["limiter", "concurrency limit", "parallel limit"], "main": "index.js", "scripts": {"test": "make test-all", "blanket": {"pattern": "bagpipe/lib"}, "travis-cov": {"threshold": 99}}, "author": "<PERSON>", "license": "MIT", "readmeFilename": "README.md", "gitHead": "75269c60fdbb897be14575e70a2b86cf43999500", "directories": {"doc": "doc", "test": "test"}, "devDependencies": {"pedding": "*", "should": "*", "blanket": "*", "mocha": "*", "travis-cov": "*"}, "repository": {"type": "git", "url": "git://github.com/JacksonTian/bagpipe.git"}, "bugs": {"url": "https://github.com/JacksonTian/bagpipe/issues"}}