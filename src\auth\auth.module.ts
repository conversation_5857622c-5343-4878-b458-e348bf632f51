import { Modu<PERSON> } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { SessionStrategy } from "./session.strategy";
import { UserModule } from "src/user/user.module";
import { EmailModule } from "src/email/email.module";
import { AuthService } from "./auth.service";
import { AuthController } from "./http/auth.controller";
import { AuthResolver } from "./gql/auth.resolver";
@Module({
    imports: [
        EmailModule,
        UserModule,
    ],
    controllers: [AuthController],
    providers: [AuthResolver, AuthService, SessionStrategy],
})
export class AuthModule { }
