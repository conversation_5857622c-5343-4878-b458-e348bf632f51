import {
    Controller,
    Post,
    Body,
    Get,
    UseGuards,
    Ip,
    Headers,
    UnauthorizedException,
    Res,
    HttpStatus,
    HttpCode,
} from "@nestjs/common";
import { z, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>od<PERSON><PERSON><PERSON> } from "src/zod";
import { UserService } from "src/user/user.service";
import { CurrentUser } from "../types";
import { AuthService } from "../auth.service";
import * as Dto from "./dto";
import { HttpJwtAuthGuard } from "./jwt-auth.guard";
import { HttpCurrentUser } from "./current-user.decorator";
import { Cookies } from "src/decorators/cookies.decorator";
import { Response } from "express";

type Token = {
    value: string;
    expiresAt: Date;
};

@Controller("auth")
export class AuthController {
    private readonly useSecureCookies: boolean;

    constructor(
        private readonly authService: AuthService,
        private readonly userService: UserService,
    ) {
        this.useSecureCookies = z.coerce
            .boolean()
            .default(false)
            .parse(process.env.AUTH_USE_SECURE_COOKIES);
    }

    @Get("test")
    async test() {
        return true;
    }

    @Get("me")
    @UseGuards(new HttpJwtAuthGuard())
    async me(@HttpCurrentUser() currentUser: CurrentUser) {
        const user = await this.userService.getOne(currentUser.id);

        if (!user) {
            throw new UnauthorizedException();
        }

        return ZodHelper.parseInput(Dto.Me, {
            id: user.id,
            email: user.email,
            role: user.role,
            name: user.name,
            description: user.description,
            images: user.images,
            joinedAt: user.createdAt,
        });
    }

    @Post("otp")
    @HttpCode(HttpStatus.CREATED)
    async otp(
        @Body(new ZodPipe(Dto.Otp)) body: Dto.Otp,
        @Ip() ipAddress: string,
        @Headers("user-agent") userAgent: string,
    ) {
        const isSent = await this.authService.otp({
            ...body,
            ipAddress,
            userAgent,
        });

        return ZodHelper.parseInput(z.object({ isSent: z.boolean() }), {
            isSent,
        });
    }

    private setTokenCookies(
        res: Response,
        tokens: { accessToken: Token; refreshToken: Token },
    ) {
        res.cookie("access_token", tokens.accessToken.value, {
            httpOnly: true,
            secure: this.useSecureCookies,
            sameSite: "strict",
            // path: "/api",
            path: "/",
            expires: tokens.accessToken.expiresAt,
        });

        res.cookie("refresh_token", tokens.refreshToken.value, {
            httpOnly: true,
            secure: this.useSecureCookies,
            sameSite: "strict",
            // path: "/api/auth/refresh",
            path: "/",
            expires: tokens.refreshToken.expiresAt,
        });
    }

    private clearTokenCookies(res: Response) {
        res.clearCookie("access_token", {
            httpOnly: true,
            secure: this.useSecureCookies,
            sameSite: "strict",
            // path: "/api",
            path: "/",
        });

        res.clearCookie("refresh_token", {
            httpOnly: true,
            secure: this.useSecureCookies,
            sameSite: "strict",
            // path: "/api/auth/refresh",
            path: "/",
        });
    }

    @Post("register")
    @HttpCode(HttpStatus.CREATED)
    async register(
        @Res({ passthrough: true }) res: Response,
        @Body(new ZodPipe(Dto.Register)) body: Dto.Register,
        @Ip() ipAddress: string,
        @Headers("user-agent") userAgent: string,
    ) {
        const { user, ...tokens } = await this.authService.register({
            ...body,
            referrerId: body.referrerId ?? null,
            ipAddress,
            userAgent,
        });

        this.setTokenCookies(res, tokens);

        return user;
    }

    @Post("login")
    @HttpCode(HttpStatus.OK)
    async login(
        @Res({ passthrough: true }) res: Response,
        @Body(new ZodPipe(Dto.Login)) body: Dto.Login,
        @Ip() ipAddress: string,
        @Headers("user-agent") userAgent: string,
    ) {
        const { user, ...tokens } = await this.authService.login({
            ...body,
            ipAddress,
            userAgent,
        });

        this.setTokenCookies(res, tokens);

        return user;
    }

    @Get("refresh")
    @HttpCode(HttpStatus.OK)
    async refresh(
        @Res({ passthrough: true }) res: Response,
        @Cookies("refresh_token", new ZodPipe(z.string().nonempty()))
        refreshToken: string,
        @Ip() ipAddress: string,
        @Headers("user-agent") userAgent: string,
    ) {
        console.log(
            "AuthController.refresh.request.refreshToken",
            refreshToken,
        );

        const tokens = await this.authService.refresh({
            refreshToken,
            ipAddress,
            userAgent,
        });

        this.setTokenCookies(res, tokens);
    }

    @Get("sign-out")
    @HttpCode(HttpStatus.OK)
    async signOut(@Res({ passthrough: true }) res: Response) {
        this.clearTokenCookies(res);
    }
}
