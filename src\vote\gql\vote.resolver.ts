import { Args, Mutation, Query, Resolver } from "@nestjs/graphql";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>od<PERSON><PERSON><PERSON> } from "src/zod";
import * as Gql from "src/graphql";
import { VoteService } from "../vote.service";
import * as Dto from "./dto";
import { CurrentUser } from "src/auth/types";
import { GqlCurrentUser } from "src/auth/gql/current-user.decorator";

export type IVoteResolver = Gql.IResolver<never, "createVote">;

@Resolver()
export class VoteResolver implements IVoteResolver {
    constructor(private readonly voteService: VoteService) {}

    @Mutation("createVote")
    async createVote(
        @Args("input", new ZodPipe(Dto.CreateVoteInput))
        input: Dto.CreateVoteInput,
        @GqlCurrentUser() user?: CurrentUser,
    ): Promise<boolean> {
        await this.voteService.create({
            userId: user!.id,
            votingId: input.votingId,
            optionId: input.optionId,
        });

        return true;
    }
}
