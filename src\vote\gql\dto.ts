import { VoteActorType } from "@prisma/client";
import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";

export const typename = "Vote";

export type Vote = ZodHelper.Infer<typeof Vote>;
export const Vote = ZodHelper.GqlObject(typename, {
    id: ZodHelper.Uuid,

    actorType: z.nativeEnum(VoteActorType),
    actorId: ZodHelper.Uuid,

    createdAt: ZodHelper.ToDateTime,
    updatedAt: ZodHelper.ToDateTime,
});

export type CreateVoteInput = ZodHelper.Infer<typeof CreateVoteInput>;
export const CreateVoteInput = z.object({
    votingId: ZodHelper.Uuid,
    optionId: ZodHelper.Uuid,
});
