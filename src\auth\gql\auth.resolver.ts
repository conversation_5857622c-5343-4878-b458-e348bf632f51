import { UnauthorizedException, UseGuards } from "@nestjs/common";
import { Query, Resolver } from "@nestjs/graphql";
import { <PERSON>od<PERSON><PERSON><PERSON> } from "src/zod";
import * as Gql from "src/graphql.d";
import { UserService } from "src/user/user.service";
import { CurrentUser } from "../types";
import * as Dto from "./dto";
import { GqlJwtAuthGuard } from "./jwt-auth.guard";
import { GqlCurrentUser } from "./current-user.decorator";

export type IAuthResolver = Gql.IResolver<"test" | "me", never>;

@Resolver()
export class AuthResolver implements IAuthResolver {
    constructor(private readonly userService: UserService) {}

    @Query("test")
    test() {
        console.log("AuthResolver.test");

        return true;
    }

    @Query("me")
    @UseGuards(GqlJwtAuthGuard)
    async me(@GqlCurrentUser() currentUser?: CurrentUser) {
        console.log("AuthResolver.me.currentUser", currentUser);

        const user = await this.userService.getOne(currentUser!.id);

        if (!user) {
            throw new UnauthorizedException();
        }

        return ZodHelper.parseInput(Dto.Me, {
            id: user.id,
            email: user.email,
            role: user.role,
            name: user.name,
            description: user.description,
            joinedAt: user.createdAt,
        });
    }
}
