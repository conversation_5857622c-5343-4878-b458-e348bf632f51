{"name": "@types/session-file-store", "version": "1.2.5", "description": "TypeScript definitions for session-file-store", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/session-file-store", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk", "url": "https://github.com/blendsdk"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "rokt33r", "url": "https://github.com/rokt33r"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/session-file-store"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/express-session": "*"}, "typesPublisherContentHash": "655c81a9baa94d457cc33b50b828b6110f11f2cdf528f092016fbb4d355ad8ff", "typeScriptVersion": "4.5"}