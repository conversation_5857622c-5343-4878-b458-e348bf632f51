import { Args, Mutation, Query, Resolver } from "@nestjs/graphql";
import { NotFoundException, UseGuards } from "@nestjs/common";
import { getError } from "src/common/errors";
import * as Gql from "src/graphql";
import { CurrentUser } from "src/auth/types";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>od<PERSON>ipe } from "src/zod";
import { GqlJwtAuthGuard } from "src/auth/gql/jwt-auth.guard";
import { GqlCurrentUser } from "src/auth/gql/current-user.decorator";
import { CommuneService } from "../commune.service";
import { CommuneMemberService } from "../commune-member.service";
import * as Dto from "./dto";

export type ICommuneResolver = Gql.IResolver<
    "getCommune" | "getCommunes" | "getCommuneMember" | "getCommuneMembers",
    | "createCommune"
    | "updateCommune"
    | "deleteCommune"
    | "createCommuneMember"
    | "updateCommuneMember"
    | "deleteCommuneMember"
>;

@Resolver()
@UseGuards(GqlJwtAuthGuard)
export class CommuneResolver implements ICommuneResolver {
    constructor(
        private readonly communeService: CommuneService,
        private readonly communeMemberService: CommuneMemberService,
    ) {}

    @Query("getCommune")
    async getCommune(
        @Args("id", new ZodPipe(ZodHelper.Uuid)) id: string,
    ): Promise<Gql.Commune> {
        const commune = await this.communeService.getOne(id);

        if (!commune) {
            throw new NotFoundException(...getError("commune_not_found"));
        }

        return ZodHelper.parseInput(Dto.Commune, commune);
    }

    @Query("getCommunes")
    async getCommunes(): Promise<Gql.Commune[]> {
        const communes = await this.communeService.getMany({
            deletedAt: null,
        });

        return ZodHelper.parseInput(Dto.Communes, communes);
    }

    @Mutation("createCommune")
    async createCommune(
        @Args("input", new ZodPipe(Dto.CreateCommuneInput))
        input: Dto.CreateCommuneInput,
        @GqlCurrentUser() user?: CurrentUser,
    ): Promise<Gql.Commune> {
        console.dir(
            {
                "CommuneResolver.createCommune.params": {
                    input,
                    user,
                },
            },
            { depth: null },
        );

        const commune = await this.communeService.create(input, user!);

        return ZodHelper.parseInput(Dto.Commune, commune);
    }

    @Mutation("updateCommune")
    async updateCommune(
        @Args("input", new ZodPipe(Dto.UpdateCommuneInput))
        input: Dto.UpdateCommuneInput,
        @GqlCurrentUser() user?: CurrentUser,
    ): Promise<Gql.Commune> {
        const { id, ...data } = input;

        const commune = await this.communeService.update(id, data, user!);

        return ZodHelper.parseInput(Dto.Commune, commune);
    }

    @Mutation("deleteCommune")
    async deleteCommune(
        @Args("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @GqlCurrentUser() user?: CurrentUser,
    ): Promise<boolean> {
        await this.communeService.softDeleteOneCascade(id, user!);

        return true;
    }

    @Query("getCommuneMember")
    async getCommuneMember(
        @Args("id", new ZodPipe(ZodHelper.Uuid)) id: string,
    ): Promise<Gql.CommuneMember> {
        const communeMember = await this.communeMemberService.getOne(id);

        if (!communeMember) {
            throw new NotFoundException(
                ...getError("commune_member_not_found"),
            );
        }

        const result = Dto.CommuneMember.parse(communeMember);

        return result;
    }

    @Query("getCommuneMembers")
    async getCommuneMembers(
        @Args("communeId", new ZodPipe(ZodHelper.Uuid)) communeId: string,
    ): Promise<Gql.CommuneMember[]> {
        const communeMembers = await this.communeMemberService.getMany({
            communeId,
            deletedAt: null,
        });

        const result = Dto.CommuneMembers.parse(communeMembers);

        return result;
    }

    @Mutation("createCommuneMember")
    async createCommuneMember(
        @Args(
            "createCommuneMemberInput",
            new ZodPipe(Dto.CreateCommuneMemberInput),
        )
        createCommuneMemberInput: Dto.CreateCommuneMemberInput,
    ): Promise<Gql.CommuneMember> {
        const communeMember = await this.communeMemberService.createOne({
            commune: {
                connect: {
                    id: createCommuneMemberInput.communeId,
                },
            },
            actorType: createCommuneMemberInput.actorType,
            actorId: createCommuneMemberInput.actorId,
        });

        const result = Dto.CommuneMember.parse(communeMember);

        return result;
    }

    @Mutation("updateCommuneMember")
    async updateCommuneMember(
        @Args(
            "updateCommuneMemberInput",
            new ZodPipe(Dto.UpdateCommuneMemberInput),
        )
        updateCommuneMemberInput: Dto.UpdateCommuneMemberInput,
    ): Promise<Gql.CommuneMember> {
        const { id, ...data } = updateCommuneMemberInput;

        const communeMember = await this.communeMemberService.updateOne(
            id,
            data,
        );

        const result = Dto.CommuneMember.parse(communeMember);

        return result;
    }

    @Mutation("deleteCommuneMember")
    async deleteCommuneMember(
        @Args("id", new ZodPipe(ZodHelper.Uuid)) id: string,
    ): Promise<boolean> {
        await this.communeMemberService.softDeleteOne(id);

        return true;
    }
}
