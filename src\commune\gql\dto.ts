import * as prisma from "@prisma/client";
import { z, ZodHelper } from "src/zod";

export const communeMemberTypename = "CommuneMember";

export type CommuneMemberType = ZodHelper.Infer<typeof CommuneMemberType>;
export const CommuneMemberType = z.nativeEnum(prisma.CommuneMemberType);

export type CommuneMember = ZodHelper.Infer<typeof CommuneMember>;
export const CommuneMember = z.object({
    __typename: ZodHelper.Typename(communeMemberTypename),
    id: ZodHelper.Uuid,
    actorType: CommuneMemberType,
    actorId: ZodHelper.Uuid,
    joinedAt: ZodHelper.ToDateTime,
    leftAt: ZodHelper.ToDateTime.nullable(),
    createdAt: ZodHelper.ToDateTime,
    updatedAt: ZodHelper.ToDateTime,
});

export const CommuneMembers = z.array(CommuneMember);

export const communeTypename = "Commune";

export type Commune = ZodHelper.Infer<typeof Commune>;
export const Commune = z.object({
    __typename: ZodHelper.Typename(communeTypename),
    id: ZodHelper.Uuid,
    members: z.array(CommuneMember).min(1),
    createdAt: ZodHelper.ToDateTime,
    updatedAt: ZodHelper.ToDateTime,
});

export const Communes = z.array(Commune);

export type CreateCommuneInput = ZodHelper.Infer<typeof CreateCommuneInput>;
export const CreateCommuneInput = z.object({
    headUserId: ZodHelper.Uuid,

    name: ZodHelper.Localizations,
    description: ZodHelper.Localizations,
});

export type UpdateCommuneInput = ZodHelper.Infer<typeof UpdateCommuneInput>;
export const UpdateCommuneInput = z.object({
    id: ZodHelper.Uuid,
});

export type CreateCommuneMemberInput = ZodHelper.Infer<
    typeof CreateCommuneMemberInput
>;
export const CreateCommuneMemberInput = z.object({
    communeId: ZodHelper.Uuid,
    actorType: CommuneMemberType,
    actorId: ZodHelper.Uuid,
});

export type UpdateCommuneMemberInput = ZodHelper.Infer<
    typeof UpdateCommuneMemberInput
>;
export const UpdateCommuneMemberInput = z.object({
    id: ZodHelper.Uuid,
});
