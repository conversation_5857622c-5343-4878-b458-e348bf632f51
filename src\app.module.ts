import { join } from "path";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { PassportModule } from "@nestjs/passport";
// import { GraphQLModule } from "@nestjs/graphql";
// import { ApolloDriver, ApolloDriverConfig } from "@nestjs/apollo";
// import { GraphQLRequest } from "@apollo/server";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { PrismaModule } from "./prisma/prisma.module";
import { UserModule } from "./user/user.module";
import { LocalizationModule } from "./localization/localization.module";
import { CommuneModule } from "./commune/commune.module";
import { VotingModule } from "./voting/voting.module";
import { VoteModule } from "./vote/vote.module";
import { AuthModule } from "./auth/auth.module";
import { EmailModule } from "./email/email.module";
import { MinioModule } from "./minio/minio.module";
import { PostModule } from "./post/post.module";
import { ReactorModule } from "./reactor/reactor.module";

@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
        }),
        // GraphQLModule.forRoot<ApolloDriverConfig>({
        //     driver: ApolloDriver,
        //     typePaths: [join(process.cwd(), "src/**/*.graphql")],
        //     context: ({ req }: { req: GraphQLRequest }) => ({ req }),
        // }),
        {
            module: PrismaModule,
            global: true,
        },
        PassportModule,
        AuthModule,
        UserModule,
        LocalizationModule,
        CommuneModule,
        VotingModule,
        VoteModule,
        EmailModule,
        MinioModule,
        PostModule,
        AuthModule,
        ReactorModule,
    ],
    controllers: [AppController],
    providers: [AppService],
})
export class AppModule {}
