import {
    Controller,
    Get,
    Post,
    Body,
    Param,
    UseGuards,
    Query,
    NotFoundException,
    Put,
    Delete,
    InternalServerErrorException,
    UseInterceptors,
    UploadedFiles,
    BadRequestException,
    ParseFilePipe,
    MaxFileSizeValidator,
    FileTypeValidator,
} from "@nestjs/common";
import { FilesInterceptor } from "@nestjs/platform-express";
import { z, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>odPipe } from "src/zod";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { HttpJwtAuthGuard } from "src/auth/http/jwt-auth.guard";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { CommuneService } from "../commune.service";
import * as Dto from "./dto";
import {
    ALLOWED_FILE_TYPES,
    MAX_FILE_SIZE,
    MAX_FILES_COUNT,
} from "./file-upload.dto";
import { CommuneMemberService } from "../commune-member.service";
import { UserService } from "src/user/user.service";

@Controller("commune")
@UseGuards(HttpJwtAuthGuard)
export class CommuneController {
    constructor(
        private readonly communeService: CommuneService,
        private readonly communeMemberService: CommuneMemberService,
        private readonly userService: UserService,
    ) {}

    private async getCommuneToReturn(
        commune: NonNullable<Awaited<ReturnType<CommuneService["getOne"]>>>,
    ) {
        const headMember = commune.members.find((member) => member.isHead);

        if (!headMember) {
            throw new InternalServerErrorException(
                getError("commune_head_member_not_found"),
            );
        }

        const headMemberEntity =
            headMember.actorType === "user"
                ? await this.userService.getOne(headMember.actorId)
                : await this.communeService.getOne(headMember.actorId);

        if (!headMemberEntity) {
            throw new InternalServerErrorException(
                getError("commune_head_member_entity_not_found"),
            );
        }

        const memberCount = commune.members.length;

        return {
            ...commune,
            headMember: {
                actorType: headMember.actorType,
                actorId: headMember.actorId,
                name: headMemberEntity.name,
            },
            memberCount,
            // Ensure images are included
            images: commune.images || [],
        };
    }

    @Get()
    async getCommunes(
        @Query("page", new ZodPipe(ZodHelper.pagination.page)) page: number,
        @Query("size", new ZodPipe(ZodHelper.pagination.size)) size: number,
    ): Promise<Dto.Commune[]> {
        const communes = await this.communeService.getMany(
            {
                deletedAt: null,
            },
            { page, size },
        );

        const communesToReturn = await Promise.all(
            communes.map((commune) => this.getCommuneToReturn(commune)),
        );

        return ZodHelper.parseInput(Dto.Communes, communesToReturn);
    }

    @Post()
    @UseInterceptors(FilesInterceptor("images", MAX_FILES_COUNT))
    async createCommune(
        @Body("data", new ZodPipe(Dto.CreateCommuneInput))
        body: Dto.CreateCommuneInput,

        @HttpCurrentUser() user: CurrentUser,
        @UploadedFiles(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
                    new FileTypeValidator({
                        fileType: ALLOWED_FILE_TYPES.join("|"),
                    }),
                ],
                fileIsRequired: false,
            }),
        )
        files?: Array<Express.Multer.File>,
    ): Promise<Dto.Commune> {
        try {
            // Create the commune
            const commune = await this.communeService.create(
                {
                    headUserId: body.headUserId,
                    name: body.name,
                    description: body.description,
                },
                user,
            );

            // Upload images if provided
            if (files && files.length > 0) {
                try {
                    await this.communeService.uploadCommuneImages(
                        commune.id,
                        files,
                    );
                } catch (error) {
                    console.error("Failed to upload images:", error);
                    // Continue even if image upload fails
                }
            }

            return ZodHelper.parseInput(
                Dto.Commune,
                await this.getCommuneToReturn(commune),
            );
        } catch (error) {
            console.error("Error processing form data:", error);

            if (error instanceof z.ZodError) {
                throw new BadRequestException({
                    message: "Invalid form data",
                    errors: error.errors,
                });
            }

            throw new BadRequestException("Failed to process form data");
        }
    }

    @Post(":id/images")
    @UseInterceptors(FilesInterceptor("images", MAX_FILES_COUNT))
    async uploadCommuneImages(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @HttpCurrentUser() user: CurrentUser,
        @UploadedFiles(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
                    new FileTypeValidator({
                        fileType: ALLOWED_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        files: Array<Express.Multer.File>,
    ): Promise<ZodHelper.Image[]> {
        try {
            // Check permissions
            await this.communeService.canChange(id, user);

            if (!files || files.length === 0) {
                throw new BadRequestException("No files uploaded");
            }

            const images = await this.communeService.uploadCommuneImages(
                id,
                files,
            );

            return ZodHelper.parseInput(z.array(ZodHelper.Image), images);
        } catch (error) {
            console.error("Error uploading images:", error);

            if (error instanceof BadRequestException) {
                throw error;
            }

            throw new BadRequestException("Failed to upload images");
        }
    }

    @Get(":id")
    async getCommune(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
    ): Promise<Dto.Commune> {
        const commune = await this.communeService.getOne(id);

        if (!commune) {
            throw new NotFoundException(getError("commune_not_found"));
        }

        return ZodHelper.parseInput(
            Dto.Commune,
            await this.getCommuneToReturn(commune),
        );
    }

    @Put(":id")
    async updateCommune(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Body(new ZodPipe(Dto.UpdateCommuneInput)) body: Dto.UpdateCommuneInput,
        @HttpCurrentUser() user: CurrentUser,
    ): Promise<Dto.Commune> {
        try {
            // Check if the commune exists
            const commune = await this.communeService.getOne(id);

            if (!commune) {
                throw new NotFoundException(getError("commune_not_found"));
            }

            // Update the commune
            const updatedCommune = await this.communeService.update(
                id,
                {
                    name: {
                        deleteMany: {},
                        create: body.name.map((item) => ({
                            key: "name",
                            locale: item.locale,
                            value: item.value,
                        })),
                    },
                    description: {
                        deleteMany: {},
                        create: body.description.map((item) => ({
                            key: "description",
                            locale: item.locale,
                            value: item.value,
                        })),
                    },
                },
                user,
            );

            return ZodHelper.parseInput(
                Dto.Commune,
                await this.getCommuneToReturn(updatedCommune),
            );
        } catch (error) {
            console.error("Error updating commune:", error);

            if (error instanceof z.ZodError) {
                throw new BadRequestException({
                    message: "Invalid form data",
                    errors: error.errors,
                });
            }

            if (
                error instanceof NotFoundException ||
                error instanceof BadRequestException
            ) {
                throw error;
            }

            throw new BadRequestException("Failed to update commune");
        }
    }

    @Get(":id/member")
    async getCommuneMembers(
        @Query("page", new ZodPipe(ZodHelper.pagination.page)) page: number,
        @Query("size", new ZodPipe(ZodHelper.pagination.size)) size: number,
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
    ): Promise<Dto.CommuneMember[]> {
        const communeMembers = await this.communeMemberService.getMany(
            {
                communeId: id,
                deletedAt: null,
            },
            {
                page,
                size,
            },
        );

        return ZodHelper.parseInput(Dto.CommuneMembers, communeMembers);
    }

    @Get(":id/member/:memberId")
    async getCommuneMember(
        @Param("memberId", new ZodPipe(ZodHelper.Uuid)) memberId: string,
    ): Promise<Dto.CommuneMember> {
        const communeMember = await this.communeMemberService.getOne(memberId);

        if (!communeMember) {
            throw new NotFoundException(getError("commune_member_not_found"));
        }

        return ZodHelper.parseInput(Dto.CommuneMember, communeMember);
    }

    // create
    @Post(":id/member")
    async createCommuneMember(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Body(new ZodPipe(Dto.CreateCommuneMemberInput))
        createCommuneMemberInput: Dto.CreateCommuneMemberInput,
    ): Promise<Dto.CommuneMember> {
        const commune = await this.communeMemberService.createOne({
            commune: {
                connect: {
                    id,
                    deletedAt: null,
                },
            },
            actorType: createCommuneMemberInput.actorType,
            actorId: createCommuneMemberInput.actorId,
        });

        return ZodHelper.parseInput(Dto.CommuneMember, commune);
    }

    // update
    @Put(":id/member/:memberId")
    async updateCommuneMember(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Param("memberId", new ZodPipe(ZodHelper.Uuid)) memberId: string,
        @Body(new ZodPipe(Dto.UpdateCommuneMemberInput))
        updateCommuneMemberInput: Dto.UpdateCommuneMemberInput,
    ): Promise<Dto.CommuneMember> {
        // Use memberId parameter to ensure we're updating the correct member
        const communeMember = await this.communeMemberService.getOne(memberId);

        if (!communeMember) {
            throw new NotFoundException(getError("commune_member_not_found"));
        }

        if (communeMember.communeId !== id) {
            throw new BadRequestException(
                "Member does not belong to the specified commune",
            );
        }

        const updatedMember = await this.communeMemberService.updateOne(
            memberId, // Use memberId instead of id
            updateCommuneMemberInput,
        );

        return ZodHelper.parseInput(Dto.CommuneMember, updatedMember);
    }

    // delete
    @Delete()
    async deleteCommuneMember(
        @Query("id", new ZodPipe(ZodHelper.Uuid)) id: string,
    ): Promise<boolean> {
        await this.communeMemberService.softDeleteOne(id);

        return true;
    }
}
