import { z } from "zod";
import { <PERSON>od<PERSON>elper } from "src/zod";

export const votesRequired = z.number().int().positive();

export const votingOptionTypename = "VotingOption";

export type VotingOption = ZodHelper.Infer<typeof VotingOption>;
export const VotingOption = ZodHelper.GqlObject(votingOptionTypename, {
    id: ZodHelper.Uuid,

    title: ZodHelper.Localizations,

    createdAt: ZodHelper.ToDateTime,
    updatedAt: ZodHelper.ToDateTime,
});

export const VotingOptions = z.array(VotingOption);

export const typename = "Voting";

export type Voting = ZodHelper.Infer<typeof Voting>;
export const Voting = ZodHelper.GqlObject(typename, {
    id: ZodHelper.Uuid,

    votesRequired,
    endsAt: ZodHelper.ToDateTime,

    title: ZodHelper.Localizations,
    description: ZodHelper.Localizations,

    options: z.array(VotingOption),

    createdAt: <PERSON>od<PERSON>elper.ToDateTime,
    updatedAt: ZodHelper.ToDateTime,
});

export const Votings = z.array(Voting);

export type CreateVotingOptionInput = ZodHelper.Infer<
    typeof CreateVotingOptionInput
>;
export const CreateVotingOptionInput = z.object({
    title: ZodHelper.Localizations,
});

export type CreateVotingInput = ZodHelper.Infer<typeof CreateVotingInput>;
export const CreateVotingInput = z.object({
    votesRequired,
    endsAt: ZodHelper.ToDateTime,
    // endsAt: z.string().datetime(),

    title: ZodHelper.Localizations,
    description: ZodHelper.Localizations,

    options: z.array(CreateVotingOptionInput),
});
