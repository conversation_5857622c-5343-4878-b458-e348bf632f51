import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import cookieParser from "cookie-parser";
import session from "express-session";
import FileStore from "session-file-store";

async function bootstrap() {
    const app = await NestFactory.create(AppModule);

    // Configure session file store
    const FileStoreSession = FileStore(session);

    app.use(cookieParser());

    // Configure session middleware
    app.use(
        session({
            store: new FileStoreSession({
                path: "./sessions",
                ttl: 86400, // 1 day in seconds
                retries: 5,
                factor: 1,
                minTimeout: 50,
                maxTimeout: 86400000,
            }),
            secret: process.env.SESSION_SECRET || "your-secret-key-change-in-production",
            resave: false,
            saveUninitialized: false,
            cookie: {
                secure: process.env.NODE_ENV === "production",
                httpOnly: true,
                maxAge: 24 * 60 * 60 * 1000, // 1 day
                sameSite: "strict",
            },
        }),
    );

    // app.useGlobalPipes(new ValidationPipe());

    // Enable CORS
    app.enableCors({
        credentials: true, // Allow credentials (cookies) to be sent
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
    });

    // app.use((req: any, res: any, next: any) => {
    //     console.log(req.originalUrl);
    //     console.log(req.headers.cookie);
    //     console.log(req.body);

    //     next();
    // });

    await app.listen(4000);
}

bootstrap();
