import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import cookieParser from "cookie-parser";

async function bootstrap() {
    const app = await NestFactory.create(AppModule);

    app.use(cookieParser());
    // app.useGlobalPipes(new ValidationPipe());

    // Enable CORS
    app.enableCors();

    // app.use((req: any, res: any, next: any) => {
    //     console.log(req.originalUrl);
    //     console.log(req.headers.cookie);
    //     console.log(req.body);

    //     next();
    // });

    await app.listen(4000);
}

bootstrap();
