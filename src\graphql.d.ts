/*
 * -------------------------------------------------------
 * THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
 * -------------------------------------------------------
 */

/* tslint:disable */
/* eslint-disable */

import type { z } from "zod";

export type IResolver<
    TQuery extends keyof IQuery,
    TMutation extends keyof IMutation,
> = Pick<IQuery, TQuery> & Pick<IMutation, TMutation>;
type Satisfies<T, U extends T> = U;

type Satisfies<
    TGql extends Record<string, unknown>,
    TZod extends z.ZodTypeAny,
> =
    TGql extends z.infer<TZod>
        ? z.infer<TZod> extends TGql
            ? TGql
            : never
        : never;
export type CommuneMemberType = "commune" | "user";
export type Locale = "en" | "ru";
export type ImageSource = "fs" | "s3";
export type PostStatus = "draft" | "published" | "archived";
export type MerchandiseStatus = "hidden" | "shown";
export type UserRole = "admin" | "moderator" | "user";
export type VoteActorType = "commune" | "user";

export interface CreateCommuneInput {
    headUserId: string;
}

export interface UpdateCommuneInput {
    id: string;
}

export interface CreateCommuneMemberInput {
    communeId: string;
    actorType: CommuneMemberType;
    actorId: string;
}

export interface UpdateCommuneMemberInput {
    id: string;
}

export interface LocalizationInput {
    key: string;
    locale: Locale;
    value: string;
}

export interface CreateVoteInput {
    votingId: string;
    optionId: string;
}

export interface CreateVotingInput {
    votesRequired: number;
    endsAt: DateTime;
    title: LocalizationInput[];
    description: LocalizationInput[];
    options: CreateVotingOptionInput[];
}

export interface CreateVotingOptionInput {
    title: LocalizationInput[];
}

export interface Localization {
    locale: Locale;
    value: string;
}

export interface CurrentUser {
    __typename: "CurrentUser";
    id: string;
    email: string;
    role: UserRole;
}

export interface Me {
    __typename: "Me";
    id: string;
    email: string;
    role: UserRole;
    name: Localization[];
    description: Localization[];
    joinedAt: string;
}

export interface IQuery {
    __typename: "IQuery";
    test(): Nullable<boolean> | Promise<Nullable<boolean>>;
    me(): Me | Promise<Me>;
    getCommune(id: string): Nullable<Commune> | Promise<Nullable<Commune>>;
    getCommunes(): Commune[] | Promise<Commune[]>;
    getCommuneMember(id: string): CommuneMember | Promise<CommuneMember>;
    getCommuneMembers(
        communeId: string,
    ): CommuneMember[] | Promise<CommuneMember[]>;
    getUser(id: string): User | Promise<User>;
    getUsers(
        page: Nullable<number>,
        size: Nullable<number>,
    ): User[] | Promise<User[]>;
    getVoting(id: string): Voting | Promise<Voting>;
    getVotings(): Voting[] | Promise<Voting[]>;
}

export interface Commune {
    __typename: "Commune";
    id: string;
    members: CommuneMember[];
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface CommuneMember {
    __typename: "CommuneMember";
    id: string;
    actorType: CommuneMemberType;
    actorId: string;
    joinedAt: DateTime;
    leftAt: Nullable<DateTime>;
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface IMutation {
    __typename: "IMutation";
    createCommune(input: CreateCommuneInput): Commune | Promise<Commune>;
    updateCommune(input: UpdateCommuneInput): Commune | Promise<Commune>;
    deleteCommune(id: string): boolean | Promise<boolean>;
    createCommuneMember(
        input: CreateCommuneMemberInput,
    ): CommuneMember | Promise<CommuneMember>;
    updateCommuneMember(
        input: UpdateCommuneMemberInput,
    ): CommuneMember | Promise<CommuneMember>;
    deleteCommuneMember(id: string): boolean | Promise<boolean>;
    createVote(input: CreateVoteInput): boolean | Promise<boolean>;
    createVoting(input: CreateVotingInput): Voting | Promise<Voting>;
}

export interface Image {
    __typename: "Image";
    id: string;
    source: ImageSource;
    url: string;
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface Tag {
    __typename: "Tag";
    id: string;
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface UserTitle {
    __typename: "UserTitle";
    id: string;
    color: Nullable<string>;
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface Post {
    __typename: "Post";
    id: string;
    images: Image[];
    status: PostStatus;
    publishedAt: Nullable<DateTime>;
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface Merchandise {
    __typename: "Merchandise";
    id: string;
    category: string;
    price: number;
    images: Image[];
    tags: Tag[];
    status: MerchandiseStatus;
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface User {
    __typename: "User";
    id: string;
    email: Nullable<string>;
    role: UserRole;
    titles: UserTitle[];
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface Vote {
    __typename: "Vote";
    id: string;
    actorType: VoteActorType;
    actorId: string;
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface Voting {
    __typename: "Voting";
    id: string;
    votesRequired: number;
    endsAt: DateTime;
    title: Localization[];
    description: Localization[];
    options: VotingOption[];
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface VotingOption {
    __typename: "VotingOption";
    id: string;
    title: Localization[];
    createdAt: DateTime;
    updatedAt: DateTime;
}

export type DateTime = string | number | Date;
type Nullable<T> = T | null;

export type SatisfiesCreateCommuneInput<T extends z.ZodTypeAny> = Satisfies<
    CreateCommuneInput,
    T
>;

export type SatisfiesUpdateCommuneInput<T extends z.ZodTypeAny> = Satisfies<
    UpdateCommuneInput,
    T
>;

export type SatisfiesCreateCommuneMemberInput<T extends z.ZodTypeAny> =
    Satisfies<CreateCommuneMemberInput, T>;

export type SatisfiesUpdateCommuneMemberInput<T extends z.ZodTypeAny> =
    Satisfies<UpdateCommuneMemberInput, T>;

export type SatisfiesLocalizationInput<T extends z.ZodTypeAny> = Satisfies<
    LocalizationInput,
    T
>;

export type SatisfiesCreateVoteInput<T extends z.ZodTypeAny> = Satisfies<
    CreateVoteInput,
    T
>;

export type SatisfiesCreateVotingInput<T extends z.ZodTypeAny> = Satisfies<
    CreateVotingInput,
    T
>;

export type SatisfiesCreateVotingOptionInput<T extends z.ZodTypeAny> =
    Satisfies<CreateVotingOptionInput, T>;

export type SatisfiesLocalization<T extends z.ZodTypeAny> = Satisfies<
    Localization,
    T
>;

export type SatisfiesCurrentUser<T extends z.ZodTypeAny> = Satisfies<
    CurrentUser,
    T
>;

export type SatisfiesMe<T extends z.ZodTypeAny> = Satisfies<Me, T>;

export type SatisfiesIQuery<T extends z.ZodTypeAny> = Satisfies<IQuery, T>;

export type SatisfiesCommune<T extends z.ZodTypeAny> = Satisfies<Commune, T>;

export type SatisfiesCommuneMember<T extends z.ZodTypeAny> = Satisfies<
    CommuneMember,
    T
>;

export type SatisfiesIMutation<T extends z.ZodTypeAny> = Satisfies<
    IMutation,
    T
>;

export type SatisfiesImage<T extends z.ZodTypeAny> = Satisfies<Image, T>;

export type SatisfiesTag<T extends z.ZodTypeAny> = Satisfies<Tag, T>;

export type SatisfiesUserTitle<T extends z.ZodTypeAny> = Satisfies<
    UserTitle,
    T
>;

export type SatisfiesPost<T extends z.ZodTypeAny> = Satisfies<Post, T>;

export type SatisfiesMerchandise<T extends z.ZodTypeAny> = Satisfies<
    Merchandise,
    T
>;

export type SatisfiesUser<T extends z.ZodTypeAny> = Satisfies<User, T>;

export type SatisfiesVote<T extends z.ZodTypeAny> = Satisfies<Vote, T>;

export type SatisfiesVoting<T extends z.ZodTypeAny> = Satisfies<Voting, T>;

export type SatisfiesVotingOption<T extends z.ZodTypeAny> = Satisfies<
    VotingOption,
    T
>;
