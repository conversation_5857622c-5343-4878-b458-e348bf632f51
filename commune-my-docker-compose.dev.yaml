name: 'commune'

services:
  postgres:
    image: postgres:17.4-alpine3.21
    container_name: postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: commune
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: "rBc2R0lhgjxAxjpc9J1UZd1d35bfnxk9"
    volumes:
      - postgres-data:/usr/commune/postgres/data
    networks:
      - commune

  minio:
    image: minio/minio:RELEASE.2025-04-22T22-12-26Z-cpuv1
    container_name: minio
    restart: unless-stopped
    ports:
      - "9000:9000"  # API port
      - "9001:9001"  # Console port
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: "W9U,6V]r(;WNRq]{"
    volumes:
      - minio-data:/usr/commune/minio/data
    command: server /data --console-address ":9001"
    networks:
      - commune
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  backend:
    image: commune-backend:latest
    container_name: backend
    restart: unless-stopped
    ports:
      - "4000:4000"
    environment:
      DATABASE_URL: "*****************************************************************/commune"

      MINIO_ENDPOINT: minio
      MINIO_PORT: 9000
      MINIO_ACCESS_KEY: admin
      MINIO_SECRET_KEY: "W9U,6V]r(;WNRq]{"
      MINIO_USE_SSL: ""

      INSTANCE_NAME: "Commune Dev (local)"
      INSTANCE_EMAIL_DOMAIN: "dev.commune.my"

      ACCESS_TOKEN_SECRET: "access_token_secret_very_long_key"
      ACCESS_TOKEN_EXPIRES_IN_MINUTES: "15"

      REFRESH_TOKEN_SECRET: "refresh_token_secret_very_long_key"
      REFRESH_TOKEN_EXPIRES_IN_DAYS: "7"

      EMAIL_HOST: "mail.dev.commune.my"
      EMAIL_PORT: "587"
      EMAIL_USER: "<EMAIL>"
      EMAIL_PASSWORD: "hLE-mP6-qY9-vSU"

      OTP_EMAIL_SENDER: "noreply"
      OTP_EXPIRATION_TIME_MS: "600000"

      IGNORE_EMAIL_ERRORS: ""
      DISABLE_OTP_EMAIL: ""
      DISABLE_LOGIN_OTP_CHECK: ""
      DISABLE_REGISTER_OTP_CHECK: ""
    networks:
      - commune

  frontend:
    image: commune-frontend:latest
    container_name: frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    networks:
      - commune

networks:
  commune:
    driver: host

volumes:
  minio-data:
  postgres-data:
