# This workflow integrates Scan with GitHub's code scanning feature
# Scan is a free open-source security tool for modern DevOps teams from ShiftLeft
# Visit https://slscan.io/en/latest/integrations/code-scan for help
name: <PERSON> Scan

# This section configures the trigger for the workflow. Feel free to customize depending on your convention
on: push

jobs:
  Scan-Build:
    # Scan runs on ubuntu, mac and windows
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v1
    # Instructions
    # 1. Setup JDK, Node.js, Python etc depending on your project type
    # 2. Compile or build the project before invoking scan
    #    Example: mvn compile, or npm install or pip install goes here
    # 3. Invoke <PERSON>an with the github token. Leave the workspace empty to use relative url

    - name: Perform <PERSON>an
      uses: ShiftLeftSecurity/scan-action@master
      env:
        WORKSPACE: ""
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SCAN_AUTO_BUILD: true
      with:
        output: reports
        # <PERSON><PERSON> auto-detects the languages in your project. To override uncomment the below variable and set the type
        # type: credscan,java
        # type: python

    - name: Upload report
      uses: github/codeql-action/upload-sarif@v1
      with:
        sarif_file: reports
