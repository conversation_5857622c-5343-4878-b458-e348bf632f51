import { z } from "zod";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { PassportStrategy } from "@nestjs/passport";
import { ExtractJwt, Strategy } from "passport-jwt";
import { Request } from "express";

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, "jwt") {
    constructor(configService: ConfigService) {
        super({
            jwtFromRequest: ExtractJwt.fromExtractors([
                JwtStrategy.extractJwtFromCookie,
                ExtractJwt.fromAuthHeaderAsBearerToken(),
            ]),
            ignoreExpiration: false,
            secretOrKey: z
                .string()
                .min(32)
                .parse(configService.get("ACCESS_TOKEN_SECRET")),
        });
    }

    private static extractJwtFromCookie(req: Request) {
        const token = req.cookies["access_token"];

        return token ? token : null;
    }

    async validate(payload: any) {
        return payload;
    }
}
