Always write easily readable, maintainable and functional code.

Always try to use already installed packages.
Always use double-quotes for strings.
Always use `;`, in types too.
Always use `,`.
Always use Zod for validation.

Use 4 spaces for indentation.
Use ZodPipe for controller input validation.
Use centralized error messages from errors.ts with getError helper.
Use BaseService for common service functionality.
Use HttpJwtAuthGuard for securing endpoints.
Use HttpCurrentUser decorator to extract current user in controllers.
Use Prisma for all database interactions.
Use deletedAt field for soft deletes instead of permanently removing data.
Use canGet and canChange methods for permission checks.

Consider ESlint rules.
Consider global.d.ts file.

Name files and folders only in Nest.js style, use kebab case.

Never place a long line of code.
Never use 'I' prefix for interfaces.
Never use implicit any.

Project Summary
This is a full-stack web application with a microservices architecture:
Frontend: Next.js 15 application with React 19, using Bootstrap for UI
Backend: NestJS 11 application with GraphQL API, using Prisma with PostgreSQL for data storage
Infrastructure: Docker containerization for development and deployment
The application appears to be for a community platform called "Commune" with features including:
User authentication and management
News and content publishing
E-commerce/merchandise shop
Voting/polling system
Statistics visualization
The system is designed with internationalization support and follows modern development practices with TypeScript throughout the stack.

Based on the provided code excerpts, the application follows a modern full-stack architecture with separate frontend and backend components:
Backend Architecture
Framework: NestJS (TypeScript-based Node.js framework)
API Approaches:
REST API controllers (HTTP endpoints)
GraphQL API (using Apollo)
Database: PostgreSQL with Prisma ORM
Authentication: JWT-based auth with refresh tokens
Modular Structure: Following NestJS module pattern with clear separation of concerns:
Controllers handle HTTP requests
Services contain business logic
Resolvers handle GraphQL queries/mutations
DTOs with Zod validation for type safety
Frontend Architecture
Framework: Next.js (React framework with server components)
Routing: App Router pattern (visible in the directory structure)
UI Framework: Bootstrap for styling
API Integration:
Internal API routes for server-side operations
Authentication: Token-based auth with local storage
Key Architectural Patterns
Modular Design: Both frontend and backend are organized into feature modules
Validation: Zod for schema validation throughout the application
Service Pattern: Business logic encapsulated in services
Repository Pattern: Data access through Prisma
Guards & Middleware: For authentication and request processing
